from typing import List

import numpy as np
import pandas as pd
from datetime import datetime

from schemas.responses import FrontierPortfolio, ProductMetadata, BacktestResponse
from schemas.pydantic_models import Product, PortfolioOnDate, PortfolioPurchaseLots, AssetPurchaseLots
from repositories import etf_repository, metadata_repository
from utils.typing_util import float_array

etf_repository = etf_repository.EtfRepository()
metadata_repository = metadata_repository.MetadataRepository()


def get_annual_return(r: float_array) -> float_array:
    return (1 + r) ** 252 - 1


def get_daily_return(r: float_array) -> float_array:
    return (1 + r) ** (1 / 252) - 1

def get_annual_from_multi_period_return(r: float_array, t: int):
    return (1 + r) ** (1 / t) - 1


def get_annual_mean(df: pd.DataFrame) -> np.ndarray:
    return get_annual_arithmetic_mean_and_covariance(df)[0]


def get_annual_covariance(df: pd.DataFrame) -> np.ndarray:
    return get_annual_arithmetic_mean_and_covariance(df)[1]


def get_log_mean_and_covariance_from_arithmetic(arithmetic_mean: np.ndarray, arithmetic_covariance: np.ndarray) -> List[np.ndarray]:
    arithmetic_mean = arithmetic_mean.reshape(-1, )

    m = np.diag(1 / (1 + arithmetic_mean))

    log_covariance = np.log(m @ arithmetic_covariance @ m + 1)

    log_mean = (np.log(1 + arithmetic_mean) - np.diag(log_covariance) / 2).reshape(-1, 1)

    return [log_mean, log_covariance]


def get_arithmetic_mean_and_covariance_from_log(log_mean: np.ndarray, log_covariance: np.ndarray) -> List[np.ndarray]:
    log_mean = log_mean.reshape(-1, )

    arithmetic_mean = np.exp(log_mean + np.diag(log_covariance) / 2) - 1

    mean_outer = np.add.outer(log_mean, log_mean)
    variance_outer = np.add.outer(np.diag(log_covariance), np.diag(log_covariance)) / 2

    arithmetic_covariance = np.exp(mean_outer + variance_outer) * (np.exp(log_covariance) - 1)

    arithmetic_mean = arithmetic_mean.reshape(-1, 1)

    return [arithmetic_mean, arithmetic_covariance]


def get_annual_arithmetic_mean_and_covariance(df: pd.DataFrame) -> List[np.ndarray]:
    arithmetic_mean, arithmetic_covariance = df.mean().values.reshape(-1, 1), df.cov().values

    log_mean, log_covariance = get_log_mean_and_covariance_from_arithmetic(arithmetic_mean, arithmetic_covariance)

    annual_log_mean = 252 * log_mean
    annual_log_covariance = 252 * log_covariance

    annual_arithmetic_mean, annual_arithmetic_covariance = get_arithmetic_mean_and_covariance_from_log(annual_log_mean, annual_log_covariance)

    return [annual_arithmetic_mean, annual_arithmetic_covariance]

def change_period_of_arithmetic_mean_and_covariance(arithmetic_means: np.ndarray, arithmetic_covariance: np.ndarray, period: int) -> [np.ndarray, np.ndarray]:
    log_means, log_covariance = get_log_mean_and_covariance_from_arithmetic(arithmetic_means, arithmetic_covariance)

    new_log_means = period * log_means
    new_log_covariance = period * log_covariance

    new_arithmetic_means, new_arithmetic_covariance = get_arithmetic_mean_and_covariance_from_log(new_log_means, new_log_covariance)

    return [new_arithmetic_means, new_arithmetic_covariance]


def get_last_year_and_month(last_date: datetime = None) -> str:
    if last_date is None:
        last_date = etf_repository.get_last_date()
    year = last_date.year
    month = last_date.month
    return f'{year:04d}_{month:02d}'


def synchronize_and_fill_dates(dataframes: List[pd.DataFrame]) -> List[pd.DataFrame]:
    """
    Synchronize and forward-fill a list of DataFrames based on the union of their indices.
    Args:
        dataframes (List[pd.DataFrame]): A list of DataFrames to be synchronized and forward-filled.
    Returns:
        List[pd.DataFrame]: A list of DataFrames with synchronized indices and forward-filled values.
    """
    all_dates = dataframes[0].index
    for df in dataframes[1:]:
        all_dates = all_dates.union(df.index)

    synchronized_dataframes = []

    for df in dataframes:
        synchronized_df = df.reindex(all_dates).ffill()
        synchronized_dataframes.append(synchronized_df)

    return synchronized_dataframes


def calculate_weights_for_discrete_frontier(discrete_frontier: pd.DataFrame, prices: np.ndarray) -> (pd.DataFrame, pd.DataFrame):
    market_value_df = np.multiply(discrete_frontier, prices.T)
    weights_df = market_value_df.div(market_value_df.sum(axis=1), axis=0)
    weights_df.fillna(0, inplace=True)
    return weights_df, market_value_df.sum(axis=1)


def calculate_return_variance_sharpe(portfolios_df: pd.DataFrame, returns: np.ndarray, covariance: np.ndarray, risk_free_rate: float) -> pd.DataFrame:
    portfolios = portfolios_df.values
    portfolios_df['return'] = np.dot(portfolios, returns)
    portfolios_df['variance'] = [np.dot(weights, np.dot(covariance, weights.T)) for weights in portfolios]
    portfolios_df['variance'] = np.where(portfolios_df['variance'] < 1e-5, 0, portfolios_df['variance'])
    portfolios_df['sharpe'] = np.where(portfolios_df['variance'] == 0, 0, (portfolios_df['return'] - risk_free_rate) / np.sqrt(portfolios_df['variance']))
    return portfolios_df


def variances_and_correlation_from_covariance(covariance: pd.DataFrame) -> (pd.DataFrame, pd.DataFrame):
    variances = np.diag(covariance)
    correlation = covariance / np.sqrt(np.outer(variances, variances))

    variances = pd.DataFrame(variances.reshape(1, -1), columns=covariance.columns)
    correlation.set_index(covariance.columns, inplace=True)

    return variances, correlation


def include_ter(expected_products_returns: np.ndarray) -> np.ndarray:
    columns = metadata_repository.get_ISINs()
    products_metadata = metadata_repository.get_metadata_for_ISINs()

    etf_ter = {col: 0.0 for col in columns}
    for product in products_metadata:
        if product.assetClass != 'Risk Free':
            etf_ter[product.isin] = product.ter

    expected_returns_with_ter = expected_products_returns - np.array(list(etf_ter.values())).reshape(-1, 1)

    return expected_returns_with_ter


def calculate_market_portfolio_returns(asset_classes_returns: pd.DataFrame, asset_classes_weights: np.ndarray) -> np.ndarray:
    weighted_returns = asset_classes_returns * asset_classes_weights
    market_portfolio_returns = weighted_returns.sum(axis=1).to_numpy()
    return market_portfolio_returns


def find_max_sharpe_portfolio(weights_discrete_df: pd.DataFrame, quantities_discrete_frontier_df: pd.DataFrame) -> (pd.DataFrame, pd.DataFrame, float, float, float, int):
    optimal_index = weights_discrete_df[weights_discrete_df['FR0010510800'] == 0.0]['sharpe'].idxmax()
    return_ = weights_discrete_df['return'].iloc[optimal_index].item()
    variance = weights_discrete_df['variance'].iloc[optimal_index].item()
    sharpe = weights_discrete_df['sharpe'].iloc[optimal_index].item()

    return quantities_discrete_frontier_df.iloc[optimal_index], weights_discrete_df.iloc[optimal_index], return_, variance, sharpe, optimal_index


def create_products(portfolio: pd.DataFrame, weights: pd.DataFrame) -> List[Product]:
    products = []

    for isin in portfolio.index.values:
        products.append(Product(isin=isin,
                                weight=weights[isin],
                                quantity=portfolio[isin]))

    return products


def get_products_metadata(ISINs: List[str]) -> List[ProductMetadata]:
    products_metadata = metadata_repository.get_metadata_for_ISINs(ISINs)
    prices_df, risk_free_price_df = etf_repository.get_prices_eur()
    for product in products_metadata:
        if product.assetClass == 'Risk Free':
            product.price = risk_free_price_df[product.isin].item()
        else:
            product.price = prices_df[product.isin].item()
    return products_metadata


def create_portfolios(quantities_discrete_efficient_frontier_df: pd.DataFrame,
                      weights_discrete_efficient_frontier_df: pd.DataFrame) -> List[FrontierPortfolio]:
    portfolios = []
    for (index, portfolio), (_, weights) in zip(quantities_discrete_efficient_frontier_df.iterrows(),
                                                weights_discrete_efficient_frontier_df.iterrows()):

        return_ = weights.pop('return')
        variance = weights.pop('variance')
        sharpe = weights.pop('sharpe')

        filtered_discrete_portfolio = portfolio[portfolio != 0.0]
        filtered_weights = weights[weights != 0.0]

        products = []

        for isin in filtered_discrete_portfolio.index.values:
            products.append(Product(isin=isin,
                                    weight=filtered_weights[isin],
                                    quantity=filtered_discrete_portfolio[isin]))

        portfolios.append(FrontierPortfolio(index=index,
                                            annual_return=return_,
                                            variance=variance,
                                            sharpe_ratio=sharpe,
                                            products=products))

    return portfolios


def create_backtest_response(date_times: np.ndarray, weights: np.ndarray, values: np.ndarray) -> BacktestResponse:
    points = []
    for date_time, value, weight in zip(date_times, values, weights):
        points.append(PortfolioOnDate(point_datetime=pd.Timestamp(date_time).to_pydatetime(), portfolio_value=value,
                                      weights=weight))

    return BacktestResponse(points=points)


def sort_and_fill_portfolio_transactions(portfolio_transactions: PortfolioPurchaseLots, isin_order: list[str]) -> PortfolioPurchaseLots:
    asset_dict = {asset.isin: asset for asset in portfolio_transactions.asset_purchase_lots}

    sorted_assets = []
    new_assets = []
    for isin in isin_order:
        if isin in asset_dict:
            sorted_assets.append(asset_dict[isin])
            new_assets.append(asset_dict[isin])
        else:
            zero_asset = AssetPurchaseLots(
                isin=isin,
                purchase_lots=[]
            )
            sorted_assets.append(zero_asset)

    # Create new portfolio - from_attributes=True should handle AssetPurchaseLots objects
    sorted_portfolio_transactions = PortfolioPurchaseLots(asset_purchase_lots=sorted_assets)
    return sorted_portfolio_transactions


