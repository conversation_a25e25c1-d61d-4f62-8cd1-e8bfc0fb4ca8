#%%
%load_ext autoreload
%autoreload 2
#%%

from datetime import datetime

from main import rebalance_portfolio
from schemas.pydantic_models import PortfolioPurchaseLots, AssetPurchaseLots, PurchaseLot

asset_transactions = [AssetPurchaseLots(isin='IE00BF4RFH31', purchase_lots=[PurchaseLot(quantity_purchased=1, purchase_date=datetime.today(), unit_purchase_price=100)]), AssetPurchaseLots(isin='DE000A0S9GB0', purchase_lots=[PurchaseLot(quantity_purchased=1, purchase_date=datetime.today(), unit_purchase_price=100)]), AssetPurchaseLots(isin='IE00B4K48X80', purchase_lots=[PurchaseLot(quantity_purchased=1, purchase_date=datetime.today(), unit_purchase_price=100)])]

portfolio = PortfolioPurchaseLots(asset_purchase_lots=asset_transactions)
#%%
portfolio
#%%
isins = ['DE000A0S9GB0', 'IE00BF4RFH31', 'IE00BGSF1X88', 'IE00B4K48X80']
#%%
from utils.util import sort_and_fill_portfolio_transactions

sorted_portfolio = sort_and_fill_portfolio_transactions(portfolio, isins)
#%%
sorted_portfolio
#%%
sorted_portfolio.get_quantities()

#%%
{
  "currentPortfolio": {
    "assetPurchaseLots": [
      {
        "isin": "IE00BJ0KDR00",
        "purchaseLots": [
          {
            "quantityPurchased": 10,
            "purchaseDate": "2025-05-02T10:00:00",
            "unitPurchasePrice": 146.175
          },
          {
            "quantityPurchased": 5,
            "purchaseDate": "2025-06-02T14:30:00",
            "unitPurchasePrice": 151.2
          }
        ]
      },
      {
        "isin": "IE00B4K48X80",
        "purchaseLots": [
          {
            "quantityPurchased": 20,
            "purchaseDate": "2024-12-16T09:15:00",
            "unitPurchasePrice": 79.675
          }
        ]
      }
    ]
  },
  "cash": 1000,
  "targetWeights": {
    "IE00BJ0KDR00": 0.5,
    "IE00B4K48X80": 0.5
  }
}

#%%
json = {
  "currentPortfolio": {
    "assetPurchaseLots": [
      {
        "isin": "IE00BJ0KDR00",
        "purchaseLots": [
          {
            "quantityPurchased": 10,
            "purchaseDate": "2025-05-02T10:00:00",
            "unitPurchasePrice": 146.175
          },
          {
            "quantityPurchased": 5,
            "purchaseDate": "2025-06-02T14:30:00",
            "unitPurchasePrice": 151.2
          }
        ]
      },
      {
        "isin": "IE00B4K48X80",
        "purchaseLots": [
          {
            "quantityPurchased": 20,
            "purchaseDate": "2024-12-16T09:15:00",
            "unitPurchasePrice": 79.675
          }
        ]
      }
    ]
  },
  "cash": 1000,
  "targetWeights": {
    "IE00BJ0KDR00": 0.7,
    "IE00B4K48X80": 0.3
  },
  "rebalanceConstraints": {
    "allowedCapitalGains": 0
  }
}
#%%
json = {
  "currentPortfolio": {
    "assetPurchaseLots": [
    ]
  },
  "cash": 1000,
  "targetWeights": {
    "IE00BJ0KDR00": 0.7,
    "IE00B4K48X80": 0.3
  }
}
#%%
from schemas.requests import RebalanceRequest

rebalance_request = RebalanceRequest(**json)
#%%
rebalance_request.current_portfolio.is_empty()
#%%
rebalance_request
#%%
from services import rebalance_service
#%%
response = rebalance_service.RebalanceService().rebalance_portfolio(rebalance_request)

#%%
import json

json.loads(response.model_dump_json())
#%%
rebalance_request.current_portfolio
#%%
rebalance_request.current_portfolio.get_quantities()