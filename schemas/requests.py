import numpy as np
from pydantic import BaseModel, Field, field_validator, ConfigDict, model_validator
from typing import List, Optional, Dict
from datetime import datetime
from pandas import DataFrame, Series

from schemas.pydantic_models import Product, UserPreferences, PortfolioPurchaseLots, RebalanceConstrains
from schemas.constants import TargetMeasureEnum
from utils import validators


class EfficientFrontierRequest(BaseModel):
    investment_amount: float = Field(gt=0, description='The amount the user wants to invest', default=10000, alias='investmentAmount')
    investment_horizon: float = Field(gt=0, description='Time horizon of the investment in years', default=3, alias='investmentHorizon')
    expected_returns: Optional[List[float]] = Field(None, description='The product_returns user wants to use in calculating efficient frontier', alias='expectedReturns')
    covariance_matrix: Optional[List[List[float]]] = Field(None, description='The product_covariance matrix user wants to use in calculating efficient frontier', alias='covarianceMatrix')
    calculate_on_datetime: Optional[datetime] = Field(None, description='Datetime on which efficient frontier will be calculated', alias='calculateOnDatetime')

    model_config = ConfigDict(populate_by_name=True, validate_by_name=True, validate_by_alias=True, from_attributes=True)

    @field_validator('covariance_matrix', mode='before')
    def validate_covariance_matrix(cls, matrix):
        return validators.validate_square_and_symmetric_matrix(matrix)


class BacktestRequest(BaseModel):
    investmentAmount: int = Field(gt=0, description='The amount the user wants to invest', default=10000)
    startingDatetime: datetime = Field(description='The starting calculate_on_date of the backtest.')
    endingDatetime: datetime = Field(description='The calculate_on_date when the test will be terminated.')
    portfolio: List[Product] = Field(description='The portfolio to be tested.')


# TODO add rebalance constraints
# TODO check what happens when current_portfolio is not given
# TODO add target and target measure (risk/return) so optimal strategy can be calculated
class MultiPeriodEfficientFrontierRequest(BaseModel):
    current_portfolio: Optional[PortfolioPurchaseLots] = Field(description='All purchase_lots user made until now.', alias='currentPortfolio', default=None)
    cash: Optional[float] = Field(ge=0, description='Cash on user\'s account.', default=0)
    investment_horizon: int = Field(gt=0, description='Time horizon of the investment in years.', alias='investmentHorizon')
    cash_flows: List[int] = Field(min_length=1, description='Planned cash flows over investment horizon.', alias='cashFlows')
    target_measure: TargetMeasureEnum = Field(description='Primary metric user defined for optimization.', alias='targetMeasure')
    target_value: float = Field(description='Specific numerical goal for the selected optimization metric.', alias='targetValue')
    user_preferences: UserPreferences = Field(
        default_factory=UserPreferences,
        description='User preferences for return, risk measurement, and return calculation in efficient frontier analysis.',
        alias='userPreferences')
    calculate_on_datetime: Optional[datetime] = Field(description='Datetime on which efficient frontier will be calculated', alias='calculateOnDatetime', default_factory=datetime.now)
    expected_returns: Optional[Series] = Field(None, description='The product_returns user wants to use in calculating efficient frontier', alias='expectedReturns')
    covariance_matrix: Optional[DataFrame] = Field(None, description='The product_covariance matrix user wants to use in calculating efficient frontier', alias='covarianceMatrix')

    model_config = ConfigDict(arbitrary_types_allowed=True)

    @field_validator('covariance_matrix', mode='before')
    def validate_covariance_matrix(cls, matrix):
        return validators.validate_square_and_symmetric_matrix(matrix)

    @model_validator(mode='after')
    def pad_cash_flows(self):
        cash_flows = np.array(self.cash_flows)
        if cash_flows.size < self.investment_horizon:
            pad_width = self.investment_horizon - cash_flows.size
            cash_flows = np.pad(cash_flows, (0, pad_width), 'constant', constant_values=0)
        elif cash_flows.size > self.investment_horizon:
            cash_flows = cash_flows[:self.investment_horizon]

        self.cash_flows = cash_flows.tolist()
        return self


class RebalanceRequest(BaseModel):
    current_portfolio: PortfolioPurchaseLots = Field(description='User\'s current portfolio purchase_lots.', alias='currentPortfolio')
    cash: float = Field(ge=0, description='Cash on user\'s account.')
    target_weights: Dict[str, float] = Field(description='Target weights for rebalancing.', alias='targetWeights')
    withdrawal_amount: Optional[float] = Field(default=None,ge=0, description='Amount of money user wants to withdraw.', alias='withdrawalAmount')
    rebalance_constraints: Optional[RebalanceConstrains] = Field(None, description='Constraints for rebalancing.', alias='rebalanceConstraints')
