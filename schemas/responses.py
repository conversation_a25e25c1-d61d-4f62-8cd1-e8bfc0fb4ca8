from pydantic import BaseModel, Field, field_validator, ConfigDict
from typing import List, Dict

from schemas.pydantic_models import FrontierPortfolio, ProductMetadata, PortfolioOnDate, FrontierStrategy, RebalancedPortfolio
from schemas.visualization_models import StrategyProjections, StrategyWeights
from utils import validators


class EfficientFrontierResponse(BaseModel):
    productsMetadata: List[ProductMetadata]
    annualReturns: List[float] = Field(description='Expected annual product_returns of the products')
    covarianceMatrix: List[List[float]] = Field(description='Correlation matrix of the products')
    frontier: List[FrontierPortfolio]
    optimalIndex: int = Field(ge=0, description='Index of the optimal portfolio')

    model_config = ConfigDict(populate_by_name=True, validate_by_name=True, validate_by_alias=True, from_attributes=True)

    @field_validator('covarianceMatrix', mode='before')
    def validate_covariance_matrix(cls, matrix):
        return validators.validate_square_and_symmetric_matrix(matrix)


# TODO add visualizations to response
class MultiPeriodEfficientFrontierResponse(BaseModel):
    model_config = ConfigDict(populate_by_name=True, validate_by_name=True, validate_by_alias=True, from_attributes=True)
    product_metadata: List[ProductMetadata] = Field(description='Metadata of the products', alias='productMetadata')
    portfolio_frontier: List[FrontierPortfolio] = Field(description='List of portfolios forming the efficient frontier', alias='portfolioFrontier')
    strategy_frontier: List[FrontierStrategy] = Field(description='List of strategies forming the efficient frontier', alias='strategyFrontier')
    optimal_strategy_index: int = Field(ge=0, description='Index of the optimal strategy', alias='optimalStrategyIndex')
    frontier_projections: List[StrategyProjections] = Field(description='List of projections for each strategy on the efficient frontier', alias='frontierProjections')
    frontier_weights: List[StrategyWeights] = Field(description='List of weights for each strategy on the efficient frontier', alias='frontierWeights')


class BacktestResponse(BaseModel):
    points: List[PortfolioOnDate] = Field(description='FrontierPortfolio on dates')

    model_config = ConfigDict(populate_by_name=True, validate_by_name=True, validate_by_alias=True, from_attributes=True)


class RebalanceResponse(BaseModel):
    rebalanced_portfolio: RebalancedPortfolio = Field(description='Rebalanced portfolio', alias='rebalancedPortfolio')

    model_config = ConfigDict(populate_by_name=True, validate_by_name=True, validate_by_alias=True, from_attributes=True)



