from abc import ABC, abstractmethod
from datetime import datetime
from typing import List, Optional, Tuple

import numpy as np
import cvxpy as cp

from configuration import config
from errors.errors import OptimizationError
from schemas.pydantic_models import PortfolioPurchaseLots, AssetPurchaseLots
from configuration.logging_config import get_logger

logger = get_logger(__name__)


class RebalanceOptimizerInterface(ABC):
    def __init__(self):
        self.settings = config.get_settings()

    @abstractmethod
    def rebalance_portfolio(self,
                            transactions: PortfolioPurchaseLots,
                            cash: float,
                            weights: np.ndarray,
                            covariance: np.ndarray,
                            new_prices: np.ndarray,
                            calculate_on_date: Optional[datetime] = datetime.today(),
                            allowed_trade_cost: Optional[float] = None,
                            allowed_capital_gains: Optional[float] = None
                            ) -> (np.ndarray, float, float, float):
        pass

    @abstractmethod
    def _calculate_capital_gains(self,
                                 transactions: PortfolioPurchaseLots,
                                 quantities_sold: cp.Variable,
                                 new_prices: np.ndarray,
                                 calculate_on_date: datetime
                                 ) -> Tuple[cp.Expression, List[cp.constraints.Constraint]]:
        pass

    @abstractmethod
    def _is_taxable(self, asset_transactions: AssetPurchaseLots, asset_price: float, calculate_on_date: datetime) -> np.ndarray[bool]:
        pass


class RebalanceEngine(RebalanceOptimizerInterface):
    def _is_taxable(self, asset_transactions: AssetPurchaseLots, asset_price: float, calculate_on_date: datetime) -> np.ndarray[bool]:
        logger.debug(f'Finding taxable transactions for {asset_transactions.isin}')
        transactions_delta_prices = asset_price - asset_transactions.get_prices()
        transaction_ages = asset_transactions.get_ages(calculate_on_date)

        # TODO create parametrised method for each tax code
        is_taxable = (transaction_ages < 2).astype(int) * np.where(transactions_delta_prices > 0, 1, 0) | np.where(transactions_delta_prices < 0, 1, 0)

        return is_taxable

    def _calculate_capital_gains(self,
                                 portfolio_purchase_lots: PortfolioPurchaseLots,
                                 quantities_sold: cp.Variable,
                                 new_prices: np.ndarray,
                                 calculate_on_date: datetime
                                 ) -> Tuple[cp.Expression, List[cp.constraints.Constraint], dict[str, cp.Expression]]:
        logger.debug("Starting capital gains calculation")
        M = 1e5
        epsilon = 1e-5

        capital_gains = 0

        fifo_constraints = []
        assets_sold = []
        per_asset_cg = {}

        for asset_index, asset_purchase_lots in enumerate(portfolio_purchase_lots.asset_purchase_lots):
            if asset_purchase_lots.sum_quantities() == 0:
                logger.debug(f"Skipping asset {asset_index} with zero quantity")
                continue

            logger.debug(f"Processing asset {asset_purchase_lots.isin} with {len(asset_purchase_lots.purchase_lots)} transactions")
            asset_sold = cp.Variable(asset_purchase_lots.get_prices().shape, integer=True)

            fifo_constraints.extend([
                asset_sold >= 0,
                asset_sold <= asset_purchase_lots.get_quantities(),
                cp.sum(asset_sold) == quantities_sold[asset_index]
            ])

            if len(asset_purchase_lots.purchase_lots) > 1:
                logger.debug(f"Adding FIFO constraints for asset {asset_purchase_lots.isin}")
                fifo_enforcer = cp.Variable(asset_sold.shape, boolean=True)

                fifo_constraints.extend([
                    asset_purchase_lots.get_quantities() - asset_sold <= M * (1 - fifo_enforcer),
                    asset_purchase_lots.get_quantities() - asset_sold >= epsilon * (1 - fifo_enforcer),
                    fifo_enforcer[:-1] >= fifo_enforcer[1:],
                    asset_sold[1:] <= M * fifo_enforcer[:-1],
                ])

            transactions_delta_prices = new_prices[asset_index] - asset_purchase_lots.get_prices()

            is_taxable = self._is_taxable(asset_purchase_lots, new_prices[asset_index].item(), calculate_on_date)

            logger.debug(f"Asset {asset_purchase_lots.isin}: Found {np.sum(is_taxable)} taxable transactions")
            taxable_transactions = np.multiply(is_taxable, transactions_delta_prices)

            asset_cg = cp.sum(cp.multiply(taxable_transactions, asset_sold))

            capital_gains += asset_cg

            per_asset_cg[asset_purchase_lots.isin] = asset_cg

            assets_sold.append(asset_sold)

        logger.info(f"Capital gains calculation complete with {len(fifo_constraints)} FIFO constraints")
        return capital_gains, fifo_constraints, per_asset_cg

    def rebalance_portfolio(self,
                            weights: np.ndarray,
                            portfolio_purchase_lots: PortfolioPurchaseLots,
                            new_prices: np.ndarray,
                            covariance: np.ndarray,
                            cash: float,
                            calculate_on_date: Optional[datetime] = datetime.today(),
                            allowed_trade_cost: Optional[float] = None,
                            allowed_capital_gains: Optional[float] = None
                            ) -> (np.ndarray, np.ndarray, np.ndarray, dict[str, float], np.ndarray, float):

        logger.info("Starting portfolio rebalancing")
        logger.debug(f"Input parameters: weights shape={weights.shape}, assets={len(portfolio_purchase_lots.asset_purchase_lots)}, "
                     f"cash={cash:.2f}, allowed_trade_cost={allowed_trade_cost if allowed_trade_cost else 'None'}, "
                     f"allowed_capital_gains={allowed_capital_gains if allowed_capital_gains else 'None'}")
        M = 1e5
        lambda_ = 1e-8

        weights = weights.reshape(-1, 1)
        new_prices = new_prices.reshape(-1, 1)
        quantities = portfolio_purchase_lots.get_quantities()

        logger.debug(f"Current portfolio: {quantities.T} units at prices {new_prices.T}")

        new_quantities = cp.Variable(weights.shape, integer=True)

        delta_quantities = new_quantities - quantities

        quantities_bought = cp.Variable(delta_quantities.shape, integer=True)
        quantities_sold = cp.Variable(delta_quantities.shape, integer=True)

        new_weights = cp.multiply(new_quantities, new_prices) / (np.dot(quantities.T, new_prices) + cash)

        tracking_error = cp.quad_form(weights - new_weights, covariance * M)

        z_complementarity = cp.Variable(delta_quantities.shape, boolean=True)
        z_cardinality = cp.Variable(delta_quantities.shape, boolean=True)

        trade_value = cp.multiply(quantities_bought, new_prices) + cp.multiply(quantities_sold, new_prices)

        # TODO create new method for calculating costs based on Stock Exchange
        trade_costs = cp.maximum(z_cardinality * 3, 0.0005 * trade_value)

        logger.debug("Setting up optimization constraints")
        problem_constraints = [
            new_quantities >= 0,
            new_quantities.T @ new_prices <= quantities.T @ new_prices + cash - cp.sum(trade_costs),

            delta_quantities == quantities_bought - quantities_sold,

            quantities_bought >= 0,
            quantities_sold >= 0,
            quantities_sold <= quantities,

            quantities_bought <= M * z_complementarity,
            quantities_sold <= M * (1 - z_complementarity),

            delta_quantities <= M * z_cardinality,
            delta_quantities >= -M * z_cardinality,
        ]

        if allowed_trade_cost is not None:
            problem_constraints.append(cp.sum(trade_costs) <= allowed_trade_cost)

        # TODO create method for sorting lots in current_portfolio according to different tax codes

        if portfolio_purchase_lots.is_empty():
            capital_gains = 0
            per_asset_cg = {}
        else:
            capital_gains, fifo_constraints, per_asset_cg = self._calculate_capital_gains(portfolio_purchase_lots, quantities_sold, new_prices, calculate_on_date)

            problem_constraints.extend(fifo_constraints)

            if allowed_capital_gains is not None:
                problem_constraints.append(capital_gains <= allowed_capital_gains)

        objective = tracking_error + lambda_ * M * cp.sum(z_cardinality)

        logger.info("Solving optimization problem")
        problem = cp.Problem(cp.Minimize(objective), problem_constraints)
        problem.solve(solver=cp.CPLEX, verbose=False)

        if problem.status != cp.OPTIMAL:
            logger.error(f"Optimization failed with status: {problem.status}")
            raise OptimizationError(status=problem.status)

        logger.debug("Optimization complete, calculating results")

        old_portfolio_value = np.dot(quantities.T, new_prices).item()
        new_portfolio_value = np.dot(new_quantities.value.T, new_prices).item()
        trade_costs_value = np.sum(trade_costs.value)
        new_cash = old_portfolio_value + cash - new_portfolio_value - trade_costs_value

        for asset_isin, asset_cg in per_asset_cg.items():
            per_asset_cg[asset_isin] = round(float(asset_cg.value), 2)

        if isinstance(capital_gains, cp.Expression):
            capital_gains = capital_gains.value

        logger.info(f"Rebalancing complete: tracking error={tracking_error.value.item() / M:.6f}, "
                    f"new cash={new_cash:.2f}, portfolio value={new_portfolio_value:.2f}, "
                    f"capital gains={capital_gains:.2f}, trade costs={trade_costs_value:.2f}")

        return (np.round(new_quantities.value.squeeze()),
                np.round(delta_quantities.value.squeeze()),
                new_weights.value.squeeze(),
                per_asset_cg,
                np.round(trade_costs.value.squeeze(), 2),
                tracking_error.value.item() / M)
