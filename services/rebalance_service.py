from abc import ABC, abstractmethod
from datetime import datetime
from fastapi import HTTPException

import numpy as np
import pandas as pd

from configuration import config
from errors.errors import OptimizationError
from models import benchmark_model
from optimizers.rebalance_optimizer import RebalanceEngine
from repositories import risk_free_rate_repository, etf_repository
from schemas.pydantic_models import RebalancedAsset, RebalancedPortfolio
from schemas.requests import RebalanceRequest
from schemas.responses import RebalanceResponse
from utils.util import sort_and_fill_portfolio_transactions


class RebalanceServiceInterface(ABC):
    def __init__(self):
        self.settings = config.get_settings()
        self.rebalance_optimizer = RebalanceEngine()
        self.risk_free_rate_repository = risk_free_rate_repository.RiskFreeRateRepository()
        self.model = benchmark_model.RegressionPricingModel()
        self.etf_repository = etf_repository.EtfRepository()

    @abstractmethod
    def rebalance_portfolio(self, request: RebalanceRequest) -> RebalanceResponse:
        pass


class RebalanceService(RebalanceServiceInterface):

    def __init__(self):
        super(RebalanceService, self).__init__()

    def rebalance_portfolio(self, request: RebalanceRequest) -> RebalanceResponse:
        date = datetime.today()

        avg_risk_free_rate = self.risk_free_rate_repository.get_avg_risk_free_rate_in_time_horizon(self.settings.time_horizon, date)
        current_risk_free_rate = self.risk_free_rate_repository.get_last_risk_free_rate(date)

        prices_df, risk_free_price_df = self.etf_repository.get_prices_eur(date)
        prices_df = pd.concat([prices_df, risk_free_price_df], axis=1)

        weights = np.array([request.target_weights.get(col, 0.0) for col in prices_df.columns.values])

        if request.current_portfolio is not request.current_portfolio.is_empty():
            current_portfolio = sort_and_fill_portfolio_transactions(request.current_portfolio, prices_df.columns.values)
        else:
            current_portfolio = request.current_portfolio

        cash = -request.withdrawal_amount if request.withdrawal_amount is not None else request.cash

        allowed_trade_cost = (
            request.rebalance_constraints.allowed_transaction_fees
            if request.rebalance_constraints else None
        )
        allowed_capital_gains = (
            request.rebalance_constraints.allowed_capital_gains
            if request.rebalance_constraints else None
        )

        _, covariance = self.model.calculate_products_returns_and_covariance(avg_risk_free_rate,
                                                                             current_risk_free_rate,
                                                                             date,
                                                                             save_analytics=False)
        try:
            (
                new_quantities,
                delta_quantities,
                new_weights,
                per_asset_cg,
                trade_costs,
                _
            ) = self.rebalance_optimizer.rebalance_portfolio(
                weights=weights,
                portfolio_purchase_lots=current_portfolio,
                new_prices=prices_df.values,
                covariance=covariance,
                cash=cash,
                calculate_on_date=date,
                allowed_trade_cost=allowed_trade_cost,
                allowed_capital_gains=allowed_capital_gains
            )
        except OptimizationError as e:
            raise HTTPException(status_code=500, detail=f"Portfolio rebalancing failed: {e.status}")

        new_weights = np.where(new_quantities == 0, 0, np.round(new_weights, 6))

        isins = prices_df.columns.values

        new_quantities_dict = dict(zip(isins, new_quantities))
        delta_quantities_dict = dict(zip(isins, delta_quantities))
        new_weights_dict = dict(zip(isins, new_weights))
        transaction_fees_dict = dict(zip(isins, trade_costs))

        rebalanced_assets = [
            RebalancedAsset(
                isin=isin,
                new_quantity=new_quantities_dict[isin],
                delta_quantity=delta_quantities_dict[isin],
                new_weight=new_weights_dict[isin],
                capital_gain=per_asset_cg.get(isin, 0.0),
                transaction_fee=transaction_fees_dict[isin]
            )
            for isin in isins
        ]

        rebalanced_portfolio = RebalancedPortfolio(rebalanced_assets=rebalanced_assets)
        response = RebalanceResponse(rebalanced_portfolio=rebalanced_portfolio)

        return response
